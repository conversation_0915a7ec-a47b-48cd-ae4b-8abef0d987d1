import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, CacheHandlerContext, CacheHandlerValue } from './';
export default class FetchCache implements CacheHandler {
    private headers;
    private cacheEndpoint?;
    private debug;
    static isAvailable(ctx: {
        _requestHeaders: CacheHandlerContext['_requestHeaders'];
    }): boolean;
    constructor(ctx: CacheHandlerContext);
    revalidateTag(tag: string): Promise<void>;
    get(...args: Parameters<CacheHandler['get']>): Promise<CacheHandlerValue | null>;
    set(...args: Parameters<CacheHandler['set']>): Promise<void>;
}
