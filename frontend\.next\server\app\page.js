/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDNTI2JTVDRGVza3RvcCU1Q2hhY2thdGhvbiU1Q3Z0Yi1oYWNrJTVDYXJjJTVDZnJvbnRlbmQlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb252ZXJzYXRpb25hbC1haS1mcm9udGVuZC8/Y2ZmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXDUyNlxcXFxEZXNrdG9wXFxcXGhhY2thdGhvblxcXFx2dGItaGFja1xcXFxhcmNcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp%5Cglobals.css&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp%5Cglobals.css&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ConversationInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ConversationInterface */ \"(ssr)/./components/ConversationInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* harmony import */ var _components_StatusIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/StatusIndicator */ \"(ssr)/./components/StatusIndicator.tsx\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(ssr)/./hooks/useWebSocket.ts\");\n/* harmony import */ var _hooks_useAudioRecorder__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAudioRecorder */ \"(ssr)/./hooks/useAudioRecorder.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"disconnected\");\n    const { socket, isConnected: wsConnected, connect, disconnect, sendMessage } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_5__.useWebSocket)({\n        url: \"ws://localhost:8000/ws\" || 0,\n        onConnect: ()=>{\n            setIsConnected(true);\n            setConnectionStatus(\"connected\");\n        },\n        onDisconnect: ()=>{\n            setIsConnected(false);\n            setConnectionStatus(\"disconnected\");\n        },\n        onError: (error)=>{\n            console.error(\"WebSocket error:\", error);\n            setConnectionStatus(\"disconnected\");\n        }\n    });\n    const { isRecording, startRecording, stopRecording, audioLevel } = (0,_hooks_useAudioRecorder__WEBPACK_IMPORTED_MODULE_6__.useAudioRecorder)({\n        onAudioData: (audioData)=>{\n            if (wsConnected && audioData) {\n                sendMessage({\n                    type: \"audio_chunk\",\n                    audio_data: audioData\n                });\n            }\n        }\n    });\n    const handleConnect = async ()=>{\n        setConnectionStatus(\"connecting\");\n        try {\n            await connect();\n        } catch (error) {\n            console.error(\"Failed to connect:\", error);\n            setConnectionStatus(\"disconnected\");\n        }\n    };\n    const handleDisconnect = ()=>{\n        disconnect();\n        if (isRecording) {\n            stopRecording();\n        }\n    };\n    const handleStartConversation = async ()=>{\n        if (!wsConnected) {\n            await handleConnect();\n        }\n        // Send start conversation message\n        sendMessage({\n            type: \"start_conversation\"\n        });\n        // Start recording\n        await startRecording();\n    };\n    const handleEndConversation = ()=>{\n        // Send end conversation message\n        sendMessage({\n            type: \"end_conversation\"\n        });\n        // Stop recording\n        stopRecording();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatusIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                connectionStatus: connectionStatus,\n                                isRecording: isRecording,\n                                audioLevel: audioLevel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConversationInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                socket: socket,\n                                isConnected: wsConnected,\n                                isRecording: isRecording,\n                                onConnect: handleConnect,\n                                onDisconnect: handleDisconnect,\n                                onStartConversation: handleStartConversation,\n                                onEndConversation: handleEndConversation\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 card p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                    children: \"How to Use\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: '1. Click \"Connect\" to establish connection with the AI backend'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: '2. Click \"Start Conversation\" to begin voice interaction'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"3. Speak naturally - the AI will detect when you finish talking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"4. Listen to the AI's response and continue the conversation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: '5. Click \"End Conversation\" when you\\'re done'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AudioVisualizer.tsx":
/*!****************************************!*\
  !*** ./components/AudioVisualizer.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioVisualizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AudioVisualizer({ audioLevel, isActive }) {\n    const [bars, setBars] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Array(8).fill(0));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isActive) {\n            // Simulate audio bars based on audio level\n            const newBars = bars.map((_, index)=>{\n                const baseHeight = Math.random() * 0.3 + 0.1 // Base random height\n                ;\n                const levelMultiplier = audioLevel * 0.7 // Scale with actual audio level\n                ;\n                return Math.min(baseHeight + levelMultiplier, 1) // Cap at 1\n                ;\n            });\n            setBars(newBars);\n        } else {\n            setBars(new Array(8).fill(0));\n        }\n    }, [\n        audioLevel,\n        isActive\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"audio-visualizer\",\n        children: bars.map((height, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `audio-bar ${isActive ? \"active\" : \"\"}`,\n                style: {\n                    height: `${Math.max(height * 32, 4)}px`,\n                    animationDelay: `${index * 0.1}s`\n                }\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\AudioVisualizer.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\AudioVisualizer.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AudioVisualizer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ConversationInterface.tsx":
/*!**********************************************!*\
  !*** ./components/ConversationInterface.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConversationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ConversationInterface({ socket, isConnected, isRecording, onConnect, onDisconnect, onStartConversation, onEndConversation }) {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTranscript, setCurrentTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAIResponding, setIsAIResponding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversationActive, setConversationActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!socket) return;\n        const handleMessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                switch(data.type){\n                    case \"connection_established\":\n                        console.log(\"Connected to AI:\", data.message);\n                        break;\n                    case \"transcript\":\n                        if (data.is_final) {\n                            // Add user message to conversation\n                            const userMessage = {\n                                id: Date.now().toString(),\n                                type: \"user\",\n                                text: data.text,\n                                timestamp: new Date()\n                            };\n                            setMessages((prev)=>[\n                                    ...prev,\n                                    userMessage\n                                ]);\n                            setCurrentTranscript(\"\");\n                        } else {\n                            // Update current transcript (partial or streaming)\n                            const transcriptText = data.text || data.partial_transcript || \"\";\n                            setCurrentTranscript(transcriptText);\n                        }\n                        break;\n                    case \"ai_response\":\n                        setIsAIResponding(false);\n                        // Add AI message to conversation\n                        const aiMessage = {\n                            id: Date.now().toString(),\n                            type: \"ai\",\n                            text: data.text,\n                            timestamp: new Date(),\n                            audioData: data.audio_data\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                aiMessage\n                            ]);\n                        // Play AI response audio\n                        if (data.audio_data) {\n                            playAudioResponse(data.audio_data);\n                        }\n                        break;\n                    case \"conversation_started\":\n                        setConversationActive(true);\n                        break;\n                    case \"conversation_ended\":\n                        setConversationActive(false);\n                        setCurrentTranscript(\"\");\n                        break;\n                    case \"error\":\n                        console.error(\"AI Error:\", data.message);\n                        break;\n                }\n            } catch (error) {\n                console.error(\"Error parsing WebSocket message:\", error);\n            }\n        };\n        socket.addEventListener(\"message\", handleMessage);\n        return ()=>{\n            socket.removeEventListener(\"message\", handleMessage);\n        };\n    }, [\n        socket\n    ]);\n    const playAudioResponse = async (audioData)=>{\n        try {\n            if (audioRef.current) {\n                const audioBlob = new Blob([\n                    Uint8Array.from(atob(audioData), (c)=>c.charCodeAt(0))\n                ], {\n                    type: \"audio/wav\"\n                });\n                const audioUrl = URL.createObjectURL(audioBlob);\n                audioRef.current.src = audioUrl;\n                await audioRef.current.play();\n                // Clean up URL after playing\n                audioRef.current.onended = ()=>{\n                    URL.revokeObjectURL(audioUrl);\n                };\n            }\n        } catch (error) {\n            console.error(\"Error playing audio response:\", error);\n        }\n    };\n    const handleStartConversation = ()=>{\n        onStartConversation();\n        setMessages([]);\n        setCurrentTranscript(\"\");\n    };\n    const handleEndConversation = ()=>{\n        onEndConversation();\n        setConversationActive(false);\n        setCurrentTranscript(\"\");\n    };\n    const clearConversation = ()=>{\n        setMessages([]);\n        setCurrentTranscript(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center space-x-4\",\n                children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onConnect,\n                    className: \"btn-primary flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Connect to AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        !conversationActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleStartConversation,\n                            className: \"btn-primary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEndConversation,\n                            className: \"btn-danger flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"End Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onDisconnect,\n                            className: \"btn-secondary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Disconnect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearConversation,\n                            className: \"btn-secondary\",\n                            children: \"Clear Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 min-h-[400px] max-h-[600px] overflow-y-auto\",\n                children: messages.length === 0 && !currentTranscript ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Start a conversation to see messages here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex ${message.type === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.type === \"user\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-800 shadow-sm border\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: message.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-xs mt-1 ${message.type === \"user\" ? \"text-primary-200\" : \"text-gray-500\"}`,\n                                            children: message.timestamp.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this)),\n                        currentTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-primary-200 text-primary-800 border-2 border-primary-300 border-dashed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: currentTranscript\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1 text-primary-600\",\n                                        children: \"Speaking...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 15\n                        }, this),\n                        isAIResponding && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white text-gray-800 shadow-sm border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"AI is thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            conversationActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex items-center space-x-2 px-4 py-2 rounded-full ${isRecording ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-600\"}`,\n                    children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Listening...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Ready to listen\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ConversationInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Header() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center w-10 h-10 bg-primary-600 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    className: \"w-6 h-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Conversational AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Real-time voice conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Voice Enabled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\Header.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/StatusIndicator.tsx":
/*!****************************************!*\
  !*** ./components/StatusIndicator.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatusIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Volume2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Volume2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Volume2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Volume2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Volume2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _AudioVisualizer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AudioVisualizer */ \"(ssr)/./components/AudioVisualizer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction StatusIndicator({ connectionStatus, isRecording, audioLevel }) {\n    const getConnectionStatusColor = ()=>{\n        switch(connectionStatus){\n            case \"connected\":\n                return \"text-green-600 bg-green-100\";\n            case \"connecting\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"disconnected\":\n                return \"text-red-600 bg-red-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getConnectionStatusText = ()=>{\n        switch(connectionStatus){\n            case \"connected\":\n                return \"Connected\";\n            case \"connecting\":\n                return \"Connecting...\";\n            case \"disconnected\":\n                return \"Disconnected\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center space-x-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-2 rounded-full ${getConnectionStatusColor()}`,\n                        children: connectionStatus === \"connected\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Connection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-xs ${connectionStatus === \"connected\" ? \"text-green-600\" : connectionStatus === \"connecting\" ? \"text-yellow-600\" : \"text-red-600\"}`,\n                                children: getConnectionStatusText()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-2 rounded-full ${isRecording ? \"text-red-600 bg-red-100 recording-pulse\" : \"text-gray-600 bg-gray-100\"}`,\n                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Microphone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-xs ${isRecording ? \"text-red-600\" : \"text-gray-600\"}`,\n                                children: isRecording ? \"Recording\" : \"Inactive\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 rounded-full text-blue-600 bg-blue-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Volume2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-900\",\n                                children: \"Audio Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AudioVisualizer__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                audioLevel: audioLevel,\n                                isActive: isRecording\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\StatusIndicator.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/StatusIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useAudioRecorder.ts":
/*!***********************************!*\
  !*** ./hooks/useAudioRecorder.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioRecorder: () => (/* binding */ useAudioRecorder)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAudioRecorder auto */ \nfunction useAudioRecorder({ onAudioData, sampleRate = 16000, chunkSize = 1024 }) {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [hasPermission, setHasPermission] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const analyserRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const processorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Check microphone permission on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        checkMicrophonePermission();\n    }, []);\n    const checkMicrophonePermission = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            setHasPermission(true);\n            stream.getTracks().forEach((track)=>track.stop()) // Stop the test stream\n            ;\n        } catch (error) {\n            console.error(\"Microphone permission denied:\", error);\n            setHasPermission(false);\n        }\n    };\n    const requestMicrophonePermission = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    sampleRate: sampleRate,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            setHasPermission(true);\n            return stream;\n        } catch (error) {\n            console.error(\"Failed to get microphone access:\", error);\n            setHasPermission(false);\n            throw error;\n        }\n    };\n    const startRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            if (isRecording) return;\n            // Request microphone access\n            const stream = await requestMicrophonePermission();\n            streamRef.current = stream;\n            // Create audio context\n            audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({\n                sampleRate: sampleRate\n            });\n            const audioContext = audioContextRef.current;\n            const source = audioContext.createMediaStreamSource(stream);\n            // Create analyser for audio level monitoring\n            analyserRef.current = audioContext.createAnalyser();\n            analyserRef.current.fftSize = 256;\n            source.connect(analyserRef.current);\n            // Create script processor for real-time audio processing\n            processorRef.current = audioContext.createScriptProcessor(chunkSize, 1, 1);\n            processorRef.current.onaudioprocess = (event)=>{\n                const inputBuffer = event.inputBuffer;\n                const inputData = inputBuffer.getChannelData(0);\n                // Convert to base64 and send\n                if (onAudioData) {\n                    const audioData = convertToBase64(inputData);\n                    onAudioData(audioData);\n                }\n            };\n            source.connect(processorRef.current);\n            processorRef.current.connect(audioContext.destination);\n            // Start audio level monitoring\n            startAudioLevelMonitoring();\n            setIsRecording(true);\n            console.log(\"Recording started\");\n        } catch (error) {\n            console.error(\"Failed to start recording:\", error);\n            throw error;\n        }\n    }, [\n        isRecording,\n        onAudioData,\n        sampleRate,\n        chunkSize\n    ]);\n    const stopRecording = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        try {\n            setIsRecording(false);\n            // Stop audio level monitoring\n            if (animationFrameRef.current) {\n                cancelAnimationFrame(animationFrameRef.current);\n                animationFrameRef.current = null;\n            }\n            // Clean up audio context\n            if (processorRef.current) {\n                processorRef.current.disconnect();\n                processorRef.current = null;\n            }\n            if (analyserRef.current) {\n                analyserRef.current.disconnect();\n                analyserRef.current = null;\n            }\n            if (audioContextRef.current) {\n                audioContextRef.current.close();\n                audioContextRef.current = null;\n            }\n            // Stop media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n            setAudioLevel(0);\n            console.log(\"Recording stopped\");\n        } catch (error) {\n            console.error(\"Error stopping recording:\", error);\n        }\n    }, []);\n    const startAudioLevelMonitoring = ()=>{\n        if (!analyserRef.current) return;\n        const analyser = analyserRef.current;\n        const bufferLength = analyser.frequencyBinCount;\n        const dataArray = new Uint8Array(bufferLength);\n        const updateAudioLevel = ()=>{\n            if (!analyser || !isRecording) return;\n            analyser.getByteFrequencyData(dataArray);\n            // Calculate average volume\n            let sum = 0;\n            for(let i = 0; i < bufferLength; i++){\n                sum += dataArray[i];\n            }\n            const average = sum / bufferLength;\n            const normalizedLevel = average / 255 // Normalize to 0-1\n            ;\n            setAudioLevel(normalizedLevel);\n            animationFrameRef.current = requestAnimationFrame(updateAudioLevel);\n        };\n        updateAudioLevel();\n    };\n    const convertToBase64 = (audioData)=>{\n        try {\n            // Convert Float32Array to Int16Array (16-bit PCM)\n            const int16Array = new Int16Array(audioData.length);\n            for(let i = 0; i < audioData.length; i++){\n                int16Array[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32767));\n            }\n            // Convert to bytes\n            const bytes = new Uint8Array(int16Array.buffer);\n            // Convert to base64\n            let binary = \"\";\n            for(let i = 0; i < bytes.byteLength; i++){\n                binary += String.fromCharCode(bytes[i]);\n            }\n            return btoa(binary);\n        } catch (error) {\n            console.error(\"Error converting audio to base64:\", error);\n            return \"\";\n        }\n    };\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (isRecording) {\n                stopRecording();\n            }\n        };\n    }, [\n        isRecording,\n        stopRecording\n    ]);\n    return {\n        isRecording,\n        audioLevel,\n        hasPermission,\n        startRecording,\n        stopRecording,\n        requestMicrophonePermission\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useAudioRecorder.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useWebSocket.ts":
/*!*******************************!*\
  !*** ./hooks/useWebSocket.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \nfunction useWebSocket({ url, onConnect, onDisconnect, onMessage, onError }) {\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            // Close existing connection if any\n            if (socketRef.current) {\n                socketRef.current.close();\n            }\n            const socket = new WebSocket(url);\n            socketRef.current = socket;\n            socket.onopen = ()=>{\n                console.log(\"WebSocket connected\");\n                setIsConnected(true);\n                reconnectAttempts.current = 0;\n                onConnect?.();\n            };\n            socket.onclose = (event)=>{\n                console.log(\"WebSocket disconnected:\", event.code, event.reason);\n                setIsConnected(false);\n                socketRef.current = null;\n                onDisconnect?.();\n                // Auto-reconnect logic\n                if (!event.wasClean && reconnectAttempts.current < maxReconnectAttempts) {\n                    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);\n                    console.log(`Attempting to reconnect in ${delay}ms...`);\n                    reconnectTimeoutRef.current = setTimeout(()=>{\n                        reconnectAttempts.current++;\n                        connect();\n                    }, delay);\n                }\n            };\n            socket.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                onError?.(error);\n            };\n            socket.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    onMessage?.(data);\n                } catch (error) {\n                    console.error(\"Error parsing WebSocket message:\", error);\n                }\n            };\n            return socket;\n        } catch (error) {\n            console.error(\"Failed to connect WebSocket:\", error);\n            throw error;\n        }\n    }, [\n        url,\n        onConnect,\n        onDisconnect,\n        onMessage,\n        onError\n    ]);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // Clear reconnect timeout\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n            reconnectTimeoutRef.current = null;\n        }\n        // Close socket\n        if (socketRef.current) {\n            socketRef.current.close(1000, \"User disconnected\");\n            socketRef.current = null;\n        }\n        setIsConnected(false);\n        reconnectAttempts.current = 0;\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message)=>{\n        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {\n            try {\n                socketRef.current.send(JSON.stringify(message));\n                return true;\n            } catch (error) {\n                console.error(\"Error sending WebSocket message:\", error);\n                return false;\n            }\n        } else {\n            console.warn(\"WebSocket is not connected\");\n            return false;\n        }\n    }, []);\n    return {\n        socket: socketRef.current,\n        isConnected,\n        connect,\n        disconnect,\n        sendMessage\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useWebSocket.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"986bfbaaabe8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb252ZXJzYXRpb25hbC1haS1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz85N2MzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTg2YmZiYWFhYmU4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Conversational AI\",\n    description: \"Real-time voice conversation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUM5Qiw0RUFBQ1U7Z0JBQUlELFdBQVU7MEJBQ1pKOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb252ZXJzYXRpb25hbC1haS1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdDb252ZXJzYXRpb25hbCBBSScsXG4gIGRlc2NyaXB0aW9uOiAnUmVhbC10aW1lIHZvaWNlIGNvbnZlcnNhdGlvbiB3aXRoIEFJJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tMTAwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\hackathon\vtb-hack\arc\frontend\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C526%5CDesktop%5Chackathon%5Cvtb-hack%5Carc%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();