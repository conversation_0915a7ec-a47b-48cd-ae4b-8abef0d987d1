"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ConversationInterface.tsx":
/*!**********************************************!*\
  !*** ./components/ConversationInterface.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConversationInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone-off.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,MicOff,Phone,PhoneOff,Square!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ConversationInterface(param) {\n    let { socket, isConnected, isRecording, onConnect, onDisconnect, onStartConversation, onEndConversation } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTranscript, setCurrentTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isAIResponding, setIsAIResponding] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversationActive, setConversationActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages\n    ]);\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!socket) return;\n        const handleMessage = (event)=>{\n            try {\n                const data = JSON.parse(event.data);\n                switch(data.type){\n                    case \"connection_established\":\n                        console.log(\"Connected to AI:\", data.message);\n                        break;\n                    case \"transcript\":\n                        if (data.is_final) {\n                            // Add user message to conversation\n                            const userMessage = {\n                                id: Date.now().toString(),\n                                type: \"user\",\n                                text: data.text,\n                                timestamp: new Date()\n                            };\n                            setMessages((prev)=>[\n                                    ...prev,\n                                    userMessage\n                                ]);\n                            setCurrentTranscript(\"\");\n                        } else {\n                            // Update current transcript (partial or streaming)\n                            const transcriptText = data.text || data.partial_transcript || \"\";\n                            setCurrentTranscript(transcriptText);\n                        }\n                        break;\n                    case \"ai_response\":\n                        setIsAIResponding(false);\n                        // Add AI message to conversation\n                        const aiMessage = {\n                            id: Date.now().toString(),\n                            type: \"ai\",\n                            text: data.text,\n                            timestamp: new Date(),\n                            audioData: data.audio_data\n                        };\n                        setMessages((prev)=>[\n                                ...prev,\n                                aiMessage\n                            ]);\n                        // Play AI response audio\n                        if (data.audio_data) {\n                            playAudioResponse(data.audio_data);\n                        }\n                        break;\n                    case \"conversation_started\":\n                        setConversationActive(true);\n                        break;\n                    case \"conversation_ended\":\n                        setConversationActive(false);\n                        setCurrentTranscript(\"\");\n                        break;\n                    case \"error\":\n                        console.error(\"AI Error:\", data.message);\n                        break;\n                }\n            } catch (error) {\n                console.error(\"Error parsing WebSocket message:\", error);\n            }\n        };\n        socket.addEventListener(\"message\", handleMessage);\n        return ()=>{\n            socket.removeEventListener(\"message\", handleMessage);\n        };\n    }, [\n        socket\n    ]);\n    const playAudioResponse = async (audioData)=>{\n        try {\n            if (audioRef.current) {\n                const audioBlob = new Blob([\n                    Uint8Array.from(atob(audioData), (c)=>c.charCodeAt(0))\n                ], {\n                    type: \"audio/wav\"\n                });\n                const audioUrl = URL.createObjectURL(audioBlob);\n                audioRef.current.src = audioUrl;\n                await audioRef.current.play();\n                // Clean up URL after playing\n                audioRef.current.onended = ()=>{\n                    URL.revokeObjectURL(audioUrl);\n                };\n            }\n        } catch (error) {\n            console.error(\"Error playing audio response:\", error);\n        }\n    };\n    const handleStartConversation = ()=>{\n        onStartConversation();\n        setMessages([]);\n        setCurrentTranscript(\"\");\n    };\n    const handleEndConversation = ()=>{\n        onEndConversation();\n        setConversationActive(false);\n        setCurrentTranscript(\"\");\n    };\n    const clearConversation = ()=>{\n        setMessages([]);\n        setCurrentTranscript(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center space-x-4\",\n                children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onConnect,\n                    className: \"btn-primary flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Connect to AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        !conversationActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleStartConversation,\n                            className: \"btn-primary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleEndConversation,\n                            className: \"btn-danger flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"End Conversation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onDisconnect,\n                            className: \"btn-secondary flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Disconnect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: clearConversation,\n                            className: \"btn-secondary\",\n                            children: \"Clear Chat\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-4 min-h-[400px] max-h-[600px] overflow-y-auto\",\n                children: messages.length === 0 && !currentTranscript ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Start a conversation to see messages here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(message.type === \"user\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-800 shadow-sm border\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: message.text\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs mt-1 \".concat(message.type === \"user\" ? \"text-primary-200\" : \"text-gray-500\"),\n                                            children: message.timestamp.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this)),\n                        currentTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-primary-200 text-primary-800 border-2 border-primary-300 border-dashed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: currentTranscript\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs mt-1 text-primary-600\",\n                                        children: \"Speaking...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 15\n                        }, this),\n                        isAIResponding && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white text-gray-800 shadow-sm border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"AI is thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            conversationActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex items-center space-x-2 px-4 py-2 rounded-full \".concat(isRecording ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-600\"),\n                    children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Listening...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_MicOff_Phone_PhoneOff_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Ready to listen\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                ref: audioRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\vtb-hack\\\\arc\\\\frontend\\\\components\\\\ConversationInterface.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationInterface, \"dnm+Lbh8Z7E2VtID3ZT1u8gJLz4=\");\n_c = ConversationInterface;\nvar _c;\n$RefreshReg$(_c, \"ConversationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ConversationInterface.tsx\n"));

/***/ })

});